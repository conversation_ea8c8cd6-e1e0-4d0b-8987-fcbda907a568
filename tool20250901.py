import cmd2
import sys
from Crypto.Cipher import AES
import os
from utils.scanUDSid import *
import getopt
from utils.VerifyTime import *
import logging
from progress.bar import Bar
import binascii
from scapy.contrib.cansocket import CANSocket
from utils.loadingModule import LoadingSpinner
from scapy.config import conf
from scapy.all import raw
import itertools
import argparse
import json
import time
from scapy.layers.can import CAN
from scapy.contrib.isotp import ISOTPSocket
from scapy.contrib.isotp import ISOTPNativeSocket
from scapy.contrib.automotive.uds_scan import *
from scapy.layers.can import *
from scapy.main import load_contrib
import signal
import threading
import subprocess
import scapy.layers.tuntap
import secrets
import random


def errorMsg():
    print(RED+'''Traceback (most recent call last):
  File "/usr/lib/python3.10/threading.py", line 1567, in _shutdown
    lock.acquire()
KeyboardInterrupt:'''+RESET)



def init_can():
    conf.contribs['ISOTP'] = {'use-can-isotp-kernel-module': False}
    conf.contribs['CANSocket'] = {'use-python-can': True}
    load_contrib('automotive.uds')
    load_contrib('isotp')

def init_canfd():
    conf.contribs['ISOTP'] = {'use-can-isotp-kernel-module': True}
    conf.contribs['CANSocket'] = {'use-python-can': False}
    load_contrib('automotive.uds')
    load_contrib('isotp')


logging.basicConfig(filename='can.log', level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')




logger = logging.getLogger(__name__)

global_p2_timeout = 0.1
global_p2_star_time = 5

stop_event = threading.Event()
def signal_handler(*args):
    # type: (Any) -> None
    print('\nInterrupting')
    stop_event.set()

negativeResponseCodes = {
    0x00: "POSITIVE_RESPONSE",
    0x01:"ISO SAE Reserved",
    0x02:"ISO SAE Reserved",
    0x03:"ISO SAE Reserved",
    0x04:"ISO SAE Reserved",
    0x05:"ISO SAE Reserved",
    0x06:"ISO SAE Reserved",
    0x07:"ISO SAE Reserved",
    0x08:"ISO SAE Reserved",
    0x09:"ISO SAE Reserved",
    0x0a:"ISO SAE Reserved",
    0x0b:"ISO SAE Reserved",
    0x0c:"ISO SAE Reserved",
    0x0d:"ISO SAE Reserved",
    0x0e:"ISO SAE Reserved",
    0x0f:"ISO SAE Reserved",
    0x10: "GENERAL_REJECT",
    0x11: "SERVICE_NOT_SUPPORTED",
    0x12: "SUB_FUNCTION_NOT_SUPPORTED",
    0x13: "INCORRECT_MESSAGE_LENGTH_OR_INVALID_FORMAT",
    0x14: "RESPONSE_TOO_LONG",
    0x21: "BUSY_REPEAT_REQUEST",
    0x22: "CONDITIONS_NOT_CORRECT",
    0x24: "REQUEST_SEQUENCE_ERROR",
    0x25: "NO_RESPONSE_FROM_SUBNET_COMPONENT",
    0x26: "FAILURE_PREVENTS_EXECUTION_OF_REQUESTED_ACTION",
    0x31: "REQUEST_OUT_OF_RANGE",
    0x33: "SECURITY_ACCESS_DENIED",
    0x35: "INVALID_KEY",
    0x36: "EXCEEDED_NUMBER_OF_ATTEMPTS",
    0x37: "REQUIRED_TIME_DELAY_NOT_EXPIRED",
    0x70: "UPLOAD_DOWNLOAD_NOT_ACCEPTED",
    0x71: "TRANSFER_DATA_SUSPENDED",
    0x72: "GENERAL_PROGRAMMING_FAILURE",
    0x73: "WRONG_BLOCK_SEQUENCE_COUNTER",
    0x78: "REQUEST_CORRECTLY_RECEIVED_RESPONSE_PENDING",
    0x7E: "SUB_FUNCTION_NOT_SUPPORTED_IN_ACTIVE_SESSION",
    0x7F: "SERVICE_NOT_SUPPORTED_IN_ACTIVE_SESSION",
    0x81: "RPM_TOO_HIGH",
    0x82: "RPM_TOO_LOW",
    0x83: "ENGINE_IS_RUNNING",
    0x84: "ENGINE_IS_NOT_RUNNING",
    0x85: "ENGINE_RUN_TIME_TOO_LOW",
    0x86: "TEMPERATURE_TOO_HIGH",
    0x87: "TEMPERATURE_TOO_LOW",
    0x88: "VEHICLE_SPEED_TOO_HIGH",
    0x89: "VEHICLE_SPEED_TOO_LOW",
    0x8A: "THROTTLE_PEDAL_TOO_HIGH",
    0x8B: "THROTTLE_PEDAL_TOO_LOW",
    0x8C: "TRANSMISSION_RANGE_NOT_IN_NEUTRAL",
    0x8D: "TRANSMISSION_RANGE_NOT_IN_GEAR",
    0x8F: "BRAKE_SWITCHES_NOT_CLOSED",
    0x90: "SHIFT_LEVER_NOT_IN_PARK",
    0x91: "TORQUE_CONVERTER_CLUTCH_LOCKED",
    0x92: "VOLTAGE_TOO_HIGH",
    0x93: "VOLTAGE_TOO_LOW",
    0x74:"ISO SAE Reserved",
    0x75:"ISO SAE Reserved",
    0x76:"ISO SAE Reserved",
    0x77:"ISO SAE Reserved",
    0x27:"ISO SAE Reserved",
    0x28:"ISO SAE Reserved",
    0x29:"ISO SAE Reserved",
    0x2a:"ISO SAE Reserved",
    0x2b:"ISO SAE Reserved",
    0x2c:"ISO SAE Reserved",
    0x2d:"ISO SAE Reserved",
    0x2e:"ISO SAE Reserved",
    0x2f:"ISO SAE Reserved",
    0x30:"ISO SAE Reserved",
    0x32:"ISO SAE Reserved",
    0x34:"ISO SAE Reserved",
    0x38:"Reserved By Extended Data Link Security Document",
    0x39:"Reserved By Extended Data Link Security Document",
    0x3a:"Reserved By Extended Data Link Security Document",
    0x3b:"Reserved By Extended Data Link Security Document",
    0x3c:"Reserved By Extended Data Link Security Document",
    0x3d:"Reserved By Extended Data Link Security Document",
    0x3e:"Reserved By Extended Data Link Security Document",
    0x3f:"Reserved By Extended Data Link Security Document",
    0x40:"Reserved By Extended Data Link Security Document",
    0x41:"Reserved By Extended Data Link Security Document",
    0x42:"Reserved By Extended Data Link Security Document",
    0x43:"Reserved By Extended Data Link Security Document",
    0x44:"Reserved By Extended Data Link Security Document",
    0x45:"Reserved By Extended Data Link Security Document",
    0x46:"Reserved By Extended Data Link Security Document",
    0x47:"Reserved By Extended Data Link Security Document",
    0x48:"Reserved By Extended Data Link Security Document",
    0x49:"Reserved By Extended Data Link Security Document",
    0x4a:"Reserved By Extended Data Link Security Document",
    0x4b:"Reserved By Extended Data Link Security Document",
    0x4c:"Reserved By Extended Data Link Security Document",
    0x4d:"Reserved By Extended Data Link Security Document",
    0x4e:"Reserved By Extended Data Link Security Document",
    0x4f:"Reserved By Extended Data Link Security Document",
    0x50:"ISO SAE Reserved",
    0x51:"ISO SAE Reserved",
    0x52:"ISO SAE Reserved",
    0x53:"ISO SAE Reserved",
    0x54:"ISO SAE Reserved",
    0x55:"ISO SAE Reserved",
    0x56:"ISO SAE Reserved",
    0x57:"ISO SAE Reserved",
    0x58:"ISO SAE Reserved",
    0x59:"ISO SAE Reserved",
    0x5a:"ISO SAE Reserved",
    0x5b:"ISO SAE Reserved",
    0x5c:"ISO SAE Reserved",
    0x5d:"ISO SAE Reserved",
    0x5e:"ISO SAE Reserved",
    0x5f:"ISO SAE Reserved",
    0x60:"ISO SAE Reserved",
    0x61:"ISO SAE Reserved",
    0x62:"ISO SAE Reserved",
    0x63:"ISO SAE Reserved",
    0x64:"ISO SAE Reserved",
    0x65:"ISO SAE Reserved",
    0x66:"ISO SAE Reserved",
    0x67:"ISO SAE Reserved",
    0x68:"ISO SAE Reserved",
    0x69:"ISO SAE Reserved",
    0x6a:"ISO SAE Reserved",
    0x6b:"ISO SAE Reserved",
    0x6c:"ISO SAE Reserved",
    0x6d:"ISO SAE Reserved",
    0x6e:"ISO SAE Reserved",
    0x6f:"ISO SAE Reserved",
    0x79:"ISO SAE Reserved",
    0x7a:"ISO SAE Reserved",
    0x7b:"ISO SAE Reserved",
    0x7c:"ISO SAE Reserved",
    0x7d:"ISO SAE Reserved",
    0x80:"ISO SAE Reserved",
    0x94:"Reserved For Specific Conditions Not Correct",
    0x95:"Reserved For Specific Conditions Not Correct",
    0x96:"Reserved For Specific Conditions Not Correct",
    0x97:"Reserved For Specific Conditions Not Correct",
    0x98:"Reserved For Specific Conditions Not Correct",
    0x99:"Reserved For Specific Conditions Not Correct",
    0x9a:"Reserved For Specific Conditions Not Correct",
    0x9b:"Reserved For Specific Conditions Not Correct",
    0x9c:"Reserved For Specific Conditions Not Correct",
    0x9d:"Reserved For Specific Conditions Not Correct",
    0x9e:"Reserved For Specific Conditions Not Correct",
    0x9f:"Reserved For Specific Conditions Not Correct",
    0xa0:"Reserved For Specific Conditions Not Correct",
    0xa1:"Reserved For Specific Conditions Not Correct",
    0xa2:"Reserved For Specific Conditions Not Correct",
    0xa3:"Reserved For Specific Conditions Not Correct",
    0xa4:"Reserved For Specific Conditions Not Correct",
    0xa5:"Reserved For Specific Conditions Not Correct",
    0xa6:"Reserved For Specific Conditions Not Correct",
    0xa7:"Reserved For Specific Conditions Not Correct",
    0xa8:"Reserved For Specific Conditions Not Correct",
    0xa9:"Reserved For Specific Conditions Not Correct",
    0xaa:"Reserved For Specific Conditions Not Correct",
    0xab:"Reserved For Specific Conditions Not Correct",
    0xac:"Reserved For Specific Conditions Not Correct",
    0xad:"Reserved For Specific Conditions Not Correct",
    0xae:"Reserved For Specific Conditions Not Correct",
    0xaf:"Reserved For Specific Conditions Not Correct",
    0xb0:"Reserved For Specific Conditions Not Correct",
    0xb1:"Reserved For Specific Conditions Not Correct",
    0xb2:"Reserved For Specific Conditions Not Correct",
    0xb3:"Reserved For Specific Conditions Not Correct",
    0xb4:"Reserved For Specific Conditions Not Correct",
    0xb5:"Reserved For Specific Conditions Not Correct",
    0xb6:"Reserved For Specific Conditions Not Correct",
    0xb7:"Reserved For Specific Conditions Not Correct",
    0xb8:"Reserved For Specific Conditions Not Correct",
    0xb9:"Reserved For Specific Conditions Not Correct",
    0xba:"Reserved For Specific Conditions Not Correct",
    0xbb:"Reserved For Specific Conditions Not Correct",
    0xbc:"Reserved For Specific Conditions Not Correct",
    0xbd:"Reserved For Specific Conditions Not Correct",
    0xbe:"Reserved For Specific Conditions Not Correct",
    0xbf:"Reserved For Specific Conditions Not Correct",
    0xc0:"Reserved For Specific Conditions Not Correct",
    0xc1:"Reserved For Specific Conditions Not Correct",
    0xc2:"Reserved For Specific Conditions Not Correct",
    0xc3:"Reserved For Specific Conditions Not Correct",
    0xc4:"Reserved For Specific Conditions Not Correct",
    0xc5:"Reserved For Specific Conditions Not Correct",
    0xc6:"Reserved For Specific Conditions Not Correct",
    0xc7:"Reserved For Specific Conditions Not Correct",
    0xc8:"Reserved For Specific Conditions Not Correct",
    0xc9:"Reserved For Specific Conditions Not Correct",
    0xca:"Reserved For Specific Conditions Not Correct",
    0xcb:"Reserved For Specific Conditions Not Correct",
    0xcc:"Reserved For Specific Conditions Not Correct",
    0xcd:"Reserved For Specific Conditions Not Correct",
    0xce:"Reserved For Specific Conditions Not Correct",
    0xcf:"Reserved For Specific Conditions Not Correct",
    0xd0:"Reserved For Specific Conditions Not Correct",
    0xd1:"Reserved For Specific Conditions Not Correct",
    0xd2:"Reserved For Specific Conditions Not Correct",
    0xd3:"Reserved For Specific Conditions Not Correct",
    0xd4:"Reserved For Specific Conditions Not Correct",
    0xd5:"Reserved For Specific Conditions Not Correct",
    0xd6:"Reserved For Specific Conditions Not Correct",
    0xd7:"Reserved For Specific Conditions Not Correct",
    0xd8:"Reserved For Specific Conditions Not Correct",
    0xd9:"Reserved For Specific Conditions Not Correct",
    0xda:"Reserved For Specific Conditions Not Correct",
    0xdb:"Reserved For Specific Conditions Not Correct",
    0xdc:"Reserved For Specific Conditions Not Correct",
    0xdd:"Reserved For Specific Conditions Not Correct",
    0xde:"Reserved For Specific Conditions Not Correct",
    0xdf:"Reserved For Specific Conditions Not Correct",
    0xe0:"Reserved For Specific Conditions Not Correct",
    0xe1:"Reserved For Specific Conditions Not Correct",
    0xe2:"Reserved For Specific Conditions Not Correct",
    0xe3:"Reserved For Specific Conditions Not Correct",
    0xe4:"Reserved For Specific Conditions Not Correct",
    0xe5:"Reserved For Specific Conditions Not Correct",
    0xe6:"Reserved For Specific Conditions Not Correct",
    0xe7:"Reserved For Specific Conditions Not Correct",
    0xe8:"Reserved For Specific Conditions Not Correct",
    0xe9:"Reserved For Specific Conditions Not Correct",
    0xea:"Reserved For Specific Conditions Not Correct",
    0xeb:"Reserved For Specific Conditions Not Correct",
    0xec:"Reserved For Specific Conditions Not Correct",
    0xed:"Reserved For Specific Conditions Not Correct",
    0xee:"Reserved For Specific Conditions Not Correct",
    0xef:"Reserved For Specific Conditions Not Correct",
    0xf0:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xf1:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xf2:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xf3:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xf4:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xf5:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xf6:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xf7:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xf8:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xf9:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xfa:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xfb:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xfc:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xfd:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xfe:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xff:"ISO SAE Reserved"
}

UDS_SERVICE_NAMES = {
    0x10: "DIAGNOSTIC_SESSION_CONTROL",
    0x11: "ECU_RESET",
    0x14: "CLEAR_DIAGNOSTIC_INFORMATION",
    0x19: "READ_DTC_INFORMATION",
    0x20: "RETURN_TO_NORMAL",
    0x22: "READ_DATA_BY_IDENTIFIER",
    0x23: "READ_MEMORY_BY_ADDRESS",
    0x24: "READ_SCALING_DATA_BY_IDENTIFIER",
    0x27: "SECURITY_ACCESS",
    0x28: "COMMUNICATION_CONTROL",
    0x29: "AUTHENTICATION",
    0x2A: "READ_DATA_BY_PERIODIC_IDENTIFIER",
    0x2C: "DYNAMICALLY_DEFINE_DATA_IDENTIFIER",
    0x2D: "DEFINE_PID_BY_MEMORY_ADDRESS",
    0x2E: "WRITE_DATA_BY_IDENTIFIER",
    0x2F: "INPUT_OUTPUT_CONTROL_BY_IDENTIFIER",
    0x31: "ROUTINE_CONTROL",
    0x34: "REQUEST_DOWNLOAD",
    0x35: "REQUEST_UPLOAD",
    0x36: "TRANSFER_DATA",
    0x37: "REQUEST_TRANSFER_EXIT",
    0x38: "REQUEST_FILE_TRANSFER",
    0x3D: "WRITE_MEMORY_BY_ADDRESS",
    0x3E: "TESTER_PRESENT",
    0x7F: "NEGATIVE_RESPONSE",
    0x83: "ACCESS_TIMING_PARAMETER",
    0x84: "SECURED_DATA_TRANSMISSION",
    0x85: "CONTROL_DTC_SETTING",
    0x86: "RESPONSE_ON_EVENT",
    0x87: "LINK_CONTROL"

}

RED = "\033[1;31m"
BLUE = "\033[1;34m"
CYAN = "\033[1;36m"
WHITE = "\033[1;37m"
YELLOW = "\033[1;33m"
GREEN = "\033[1;32m"
RESET = "\033[1;0m"
BOLD = "\033[;1m"
REVERSE = "\033[;7m"


def Bitmask(cid, sid):
    if cid > 0x7ff or sid > 0x7ff:
        return cid ^ sid ^ 0x1fffffff
    else:
        return cid ^ sid ^ 0x7ff

# 定义一个函数，将十进制数转换为十六进制数，并且补齐前导零
def dec_to_hex(n, id_flag=False,digits=3):
    if n > 0x7ff and not id_flag:
        digits = 8
    hex_str = hex(n)[2:]
    zeros = digits - len(hex_str)
    if zeros > 0:
        hex_str = '0' * zeros + hex_str
    if id_flag:
        return '0x' + hex_str.upper()
    else:
        return hex_str.upper()

def my_logger(send,canid, data):
    if type(data) == UDS:
        data = binascii.b2a_hex(raw(data)).decode('utf-8')

    elif type(data) is not str:
        data = binascii.b2a_hex(data).decode('utf-8')

    data = splitWithEmpty(data)
    if send:
        logger.info('TX【'+canid+'】: --> '+data)
    else:
        logger.info('RX【'+canid+'】: <-- '+data)

def splitWithEmpty(data):
    if type(data) == bytes:
        data = binascii.b2a_hex(data).decode('utf-8')
    elif type(data) == UDS:
        data = raw(data)
        data = binascii.b2a_hex(data).decode('utf-8')
    data = data.upper()
    printdata = ' '.join(data[i:i+2] for i in range(0, len(data), 2))
    return printdata

def dealSessionData(socket,resp):
    _x = dec_to_hex(socket.tx_id,id_flag=True)
    if resp[UDS_DSCPR].diagnosticSessionType == 2:
        print('[+] '+_x+' Change Programing Session')
    elif resp[UDS_DSCPR].diagnosticSessionType == 3:
        print('[+] '+_x+' Change Extended Session')
    else:
        print('[+] '+_x+' Changed Session')
def genRandom(length):
    random_bytes = secrets.token_bytes(length)
    hex_string = secrets.token_hex(length)
    return hex_string

# def dec_to_hex(n, digits=3):
#     hex_str = hex(n)[2:]
#     zeros = digits - len(hex_str)
#     if zeros > 0:
#         hex_str = '0' * zeros + hex_str
#     return hex_str

def printSendermenu(data, datatype, socket):
    # type 1 send
    # type 0 receiver
    if datatype == 1:
        canid = dec_to_hex(socket.tx_id,id_flag=True)
        if len(data) > 1280:
            data1 = data[:640]
            printdata = splitWithEmpty(data1)
            print('S e n d【'+canid+'】:', "\033[31m"+printdata+"\033[0m",end='')
            print("\033[31m .....\033[0m",end=' ')

            data2 = data[-640:]
            printdata = splitWithEmpty(data2)
            print("\033[31m"+printdata+"\033[0m")
        else:
            printdata = splitWithEmpty(data)
            print('S e n d【'+canid+'】:',"\033[31m"+printdata+"\033[0m")
    else:
        canid = dec_to_hex(socket.rx_id,id_flag=True)
        data = splitWithEmpty(data)
        print('Receive【'+canid+'】:',"\033[32m"+data+"\033[0m")

def getseed(number, each,socket,seedTimeout):
    seed_list = []
    _x = dec_to_hex(socket.tx_id,id_flag=True)
    print('[+] '+_x+' Seed:')
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    bar = None
    prefill_done = False
    if number >= 100:
        formatlen = str(len(str(number)))
        suffixstr = '%(index)'+formatlen+'d/%(max)'+formatlen+'d'
        bar = Bar('Processing', max=number, suffix=suffixstr)

    for i in range(number):
        if stop_event is not None and stop_event.is_set():
            break
        tmpTime = seedTimeout
        resp = Sender_Receiver(socket, each)
        if resp and resp[UDS].service != 0x7f:
            seed = binascii.b2a_hex(resp[UDS_SAPR].securitySeed).decode('utf-8')
            seed_list.append(seed)
            if i < 100:
                print(seed)
            elif bar:
                if not prefill_done:
                    prefill_count = min(100, number)
                    for _ in range(prefill_count):
                        bar.next()
                    prefill_done = True
                bar.next()

            
        while tmpTime:
            if tmpTime > 0 and tmpTime < 1800:
                time.sleep(tmpTime / 1000)
                tmpTime = 0
            else:
                time.sleep(1.8)
                pkt =  UDS(binascii.a2b_hex('3e80'))
                socket.send(raw(pkt))
                my_logger(1,dec_to_hex(socket.tx_id,id_flag=True),'3E80')
                tmpTime -= 1800
    if bar:
        bar.finish()
    return seed_list

def stop_sniff(packet):
    pass  # 捕获到一个数据包后停止

def Sender_Receiver(socket,each,verbos=True,dump=False):
    if type(each) is str:
        sendData = binascii.a2b_hex(each)
    else:
        sendData = each

    isoTpMessage = ISOTP(sendData)
    uds_data = None
    my_logger(1,dec_to_hex(socket.tx_id,id_flag=True),each)
    # try:
    #     # 设置一个更长的超时时间，比如0.5秒
    #     pkg = socket.sr1(isoTpMessage,timeout=global_p2_timeout,verbose=0)
    # except (TimeoutError, warning) as e:
    #     if isinstance(e, warning):
    #         # 如果是警告，尝试清理socket缓冲区并重试
    #         socket.impl.rx_queue.clear()
    #         socket.ins.rx_queue.clear()
    #         try:
    #             pkg = socket.sr1(isoTpMessage,timeout=global_p2_timeout,verbose=0)
    #         except:
    #             pkg = None
    #     else:
    #         pkg = None

    try:
        pkg = socket.sr1(isoTpMessage,timeout=global_p2_timeout,verbose=0)
    except TimeoutError as e:
        pkg = None

    if pkg:
        uds_data = UDS(pkg.data)
        my_logger(0,dec_to_hex(socket.rx_id,id_flag=True),uds_data)


        while uds_data is not None and ((uds_data[UDS].service == 0x7f and uds_data[UDS_NR].negativeResponseCode == 0x78)):
            startTime = time.time()
            if stop_event is not None and stop_event.is_set():
                return False

            while True:
                pkg = socket.sniff(timeout=1.5, prn=stop_sniff, count=1)

                if len(pkg.res) != 0:
                    uds_data = UDS(pkg.res[0].data)
                    my_logger(0, dec_to_hex(socket.rx_id,id_flag=True), uds_data)
                    if uds_data[UDS].service == 0x7f and uds_data[UDS_NR].negativeResponseCode == 0x78:
                        startTime = time.time()  # 重新获取startTime
                    else:
                        break

                elif len(pkg.res) == 0 and time.time() - startTime <= global_p2_star_time:
                    socket.send(raw(UDS(binascii.a2b_hex('3e80'))))
                    my_logger(1, dec_to_hex(socket.tx_id,id_flag=True), '3E80')
                    continue

                elif time.time() - startTime > global_p2_star_time:
                    uds_data = None
                    break

        # while (uds_data[UDS].service == 0x7f and uds_data[UDS_NR].negativeResponseCode == 0x78):
        #     startTime = time.time()
        #     if stop_event is not None and stop_event.is_set():
        #         return False
        #     pkg = socket.sniff(timeout=1.5,prn=stop_sniff, count=1)

        #     if len(pkg.res) != 0:
        #         uds_data = UDS(pkg.res[0].data)
        #         my_logger(0,dec_to_hex(socket.rx_id),uds_data)

        #     elif len(pkg.res) == 0 and time.time() - startTime <= global_p2_star_time:
        #         socket.send(raw(UDS(binascii.a2b_hex('3e80'))))
        #         my_logger(1,dec_to_hex(socket.tx_id),'3E80')
        #         continue

        #     elif time.time() - startTime > global_p2_star_time:
        #         uds_data = None
        #         break


        while uds_data is not None and ((uds_data[UDS].service == 0x7f and uds_data[UDS_NR].requestServiceId != UDS(sendData).service) or (uds_data[UDS].service != 0x7f and uds_data[UDS].service != UDS(sendData).service + 0x40) or (uds_data.service == 0x62 and uds_data.dataIdentifier != UDS(sendData).identifiers[0]) or (uds_data.service == 0x6f and uds_data.dataIdentifier != UDS(sendData).dataIdentifier) or (uds_data.service == 0x6e and uds_data.dataIdentifier != UDS(sendData).dataIdentifier) or (uds_data.service == 0x71 and uds_data.routineIdentifier != UDS(sendData).routineIdentifier)):
            if stop_event is not None and stop_event.is_set():
                return False
            pkg = socket.sniff(timeout=global_p2_star_time,prn=stop_sniff, count=1)
            if len(pkg.res) != 0:
                uds_data = UDS(pkg.res[0].data)
                my_logger(0,dec_to_hex(socket.rx_id,id_flag=True),uds_data)
            else:
                uds_data = None
                break

    if dump:
        return uds_data

    if uds_data:
        if uds_data[UDS].service == 0x7f:

            if uds_data[UDS_NR].requestServiceId == 0x22 and verbos:
                print('0x'+binascii.b2a_hex(sendData[1:]).decode('utf-8'),'\t',negativeResponseCodes[uds_data[UDS_NR].negativeResponseCode])

            elif uds_data[UDS_NR].requestServiceId == 0x2e and verbos:
                print('0x'+binascii.b2a_hex(sendData[1:3]).decode('utf-8'),'\t',negativeResponseCodes[uds_data[UDS_NR].negativeResponseCode])

            elif verbos:
                if len(each)>36:
                    print(binascii.b2a_hex(sendData[:18]).decode('utf-8'),'......',binascii.b2a_hex(sendData[-10:]).decode('utf-8'),' ',negativeResponseCodes[uds_data[UDS_NR].negativeResponseCode])
                else:
                    print(binascii.b2a_hex(sendData).decode('utf-8'),' ',negativeResponseCodes[uds_data[UDS_NR].negativeResponseCode])
        else:
            if uds_data[UDS].service ==0x67 and uds_data[UDS].securityAccessType % 2 == 0:
                print('[+] 0x'+dec_to_hex(socket.tx_id,id_flag=True)+' Bypass 27...')

            elif uds_data[UDS].service == 0x6e:
                if type(each) is not str:
                    each = binascii.b2a_hex(each).decode('utf-8')
                print('[+]','CANID:',dec_to_hex(socket.tx_id,id_flag=True),'\tDID',each[2:6],'\t','Success')

    return uds_data


def deal_scan_data(socket, scan_range, num,f):
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    for each in scan_range:
        if stop_event is not None and stop_event.is_set():
            return False
        resp = Sender_Receiver(socket, each, verbos=False,dump=True)


        if resp == 'delay_error':
            return 'delay_error'


        if resp is None:
            continue
        if resp and (num & 0b100 == 0b100) and ((resp[UDS].service !=  0x7f) or resp[UDS_NR].negativeResponseCode != 0x11):
            if int(each[:2],16) in UDS_SERVICE_NAMES.keys():
                print('0x'+each[:2],' ', UDS_SERVICE_NAMES[int(each[:2],16)])
                f.writelines('0x'+each[:2]+' '+UDS_SERVICE_NAMES[int(each[:2],16)]+'\n')
            else:
                print('0x'+each[:2],' ', "Unknown")
                f.writelines('0x'+each[:2]+' '+"Unknown"+'\n')
        elif resp and (resp[UDS].service != 0x7f) or (((num & 0b010 == 0b010) or (num & 0b001 == 0b001)) and resp[UDS_NR].negativeResponseCode != 0x12):
            print(' '.join(each[i:i+2] for i in range(0, len(each), 2)))
            f.writelines(' '.join(each[i:i+2] for i in range(0, len(each), 2))+'\n')
    return True

# def scan_func(socketPool,num,sessionMode,send_func):
def scan_func(socketPool,num,sessionMode): 
    f = open('./data/scan_result.txt','w')
    for socket in socketPool:
        time.sleep(3)
        res = True

        if num & 0b100 == 0b100:
            if sessionMode:
                # send_func(ssocket=socket,args=sessionMode)
                for _sessiodata in sessionMode.split(' '):
                    resp = Sender_Receiver(socket,_sessiodata,verbos=True,dump=False)
                    printSendermenu(_sessiodata, 1, socket)
                    if resp:
                        printSendermenu(resp, 0, socket)
                        if resp[UDS].service == 0x50:
                            dealSessionData(socket, resp)
                    
                
            logger.info('[*] Scanning for Support Services......')
            print('[+] '+ dec_to_hex(socket.tx_id,id_flag=True) +'-' + dec_to_hex(socket.rx_id,id_flag=True)+' Support Services: ')
            f.writelines('[+] '+ dec_to_hex(socket.tx_id,id_flag=True) +'-' + dec_to_hex(socket.rx_id,id_flag=True)+' Support Services: \n')
            scan_range = (x for x in range(0x100) if not x & 0x40)
            # scan_range = (x for x in range(0x100))
            senddata = (hex(each)[2:].rjust(2,'0') + '00' for each in scan_range)

            res = deal_scan_data(socket, senddata, 0b100,f)
            logger.info('--------------END--------------')
            if res == 'delay_error':
                f.close()
                return False
            
            elif res is False:
                f.close()
                return res
            if sessionMode:
                # send_func(ssocket=socket,args='1001')
                resp = Sender_Receiver(socket,'1001',verbos=True,dump=False)
                printSendermenu('1001', 1, socket)
                if resp:
                    printSendermenu(resp, 0, socket)
                    if resp[UDS].service == 0x50:
                        dealSessionData(socket, resp)

        time.sleep(3)
        if num & 0b001 == 0b001:
            logger.info('[*] Scanning for Support Security Level......')
            print('\n',end='')
            if sessionMode:
                # send_func(ssocket=socket,args=sessionMode)
                for _sessiodata in sessionMode.split(' '):
                    resp = Sender_Receiver(socket,_sessiodata,verbos=True,dump=False)
                    printSendermenu(_sessiodata, 1, socket)
                    if resp:
                        printSendermenu(resp, 0, socket)
                        if resp[UDS].service == 0x50:
                            dealSessionData(socket, resp)
            else:
                uds_data = Sender_Receiver(socket,'1001',dump=True)
                if uds_data[UDS].service == 0x50:
                    pass
                else:
                    print(RED+'[!] Change Session Failed！！！'+RESET)
                    f.close()
                    return False

            print('[+] '+ dec_to_hex(socket.tx_id,id_flag=True) +'-' + dec_to_hex(socket.rx_id,id_flag=True)+' Security Levels: ')
            f.writelines('[+] '+ dec_to_hex(socket.tx_id,id_flag=True) +'-' + dec_to_hex(socket.rx_id,id_flag=True)+' Security Levels: \n')
            scan_range = range(1, 256, 2)
            senddata = ('27' + hex(each)[2:].rjust(2,'0') for each in scan_range)
            res = deal_scan_data(socket, senddata, 0b001,f)
            logger.info('--------------END--------------')
            if res == 'delay_error':
                f.close()
                return False
            elif res is False:
                f.close()
                return res
            if sessionMode:
                # send_func(ssocket=socket,args='1001')
                resp = Sender_Receiver(socket,'1001',verbos=True,dump=False)
                printSendermenu('1001', 1, socket)
                if resp:
                    printSendermenu(resp, 0, socket)
                    if resp[UDS].service == 0x50:
                        dealSessionData(socket, resp)

        time.sleep(3)
        if num & 0b010 == 0b010:
            session_range = range(0xff, 0, -1)
            senddata = ('10' + hex(each)[2:].rjust(2,'0') for each in session_range)
            logger.info('[*] Scanning for Support Sessions......')
            print('\n[+] '+ dec_to_hex(socket.tx_id,id_flag=True) +'-' + dec_to_hex(socket.rx_id,id_flag=True)+' Support Sessions: ')
            f.writelines('[+] '+ dec_to_hex(socket.tx_id,id_flag=True) +'-' + dec_to_hex(socket.rx_id,id_flag=True)+' Support Sessions: \n')
            res = deal_scan_data(socket, senddata, 0b010,f)
            logger.info('--------------END--------------')
            if res == 'delay_error':
                f.close()
                return False
            elif res is False:
                f.close()
                return res
            if sessionMode:
                # send_func(ssocket=socket,args='1001')
                resp = Sender_Receiver(socket,'1001',verbos=True,dump=False)
                printSendermenu('1001', 1, socket)
                if resp:
                    printSendermenu(resp, 0, socket)
                    if resp[UDS].service == 0x50:
                        dealSessionData(socket, resp)
        print('\n')
        f.write('\n')
    f.close()
    return res

def clear_queue(socketPool):
    for idx in range(len(socketPool)):
        socketPool[idx].impl.rx_queue.clear()
        socketPool[idx].ins.rx_queue.clear()

def pop_socket(socketPool,fdflag):
    if fdflag == False:
        clear_queue(socketPool)
    while len(socketPool):
        socketPool.pop().close()
    return []

def getData(socket, filename):
    with open(filename) as f:
        content = json.load(f)
        for each in content:
            if int(each[0],16) == socket.tx_id and int(each[1],16) == socket.rx_id:
                return each[2]
    return None


def jl_dssad(seed,key):
    long_key = bytes.fromhex(key)
    seed = bytes.fromhex(seed)
    # 构造数据块：前4字节为seed，后12字节填充0x0C
    data = seed + bytes([0x0C] * 12)
        
    # AES-ECB加密
    cipher = AES.new(long_key, AES.MODE_ECB)
    encrypted = cipher.encrypt(data)
        
    # 折叠16字节结果为4字节
    verify_key = bytes(
    encrypted[i] ^ encrypted[i+4] ^ encrypted[i+8] ^ encrypted[i+12]
        for i in range(4)
    )

    return verify_key.hex()


def pt2e(socket, diddata):
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 添加TX和RX ID的显示
    s_id = dec_to_hex(socket.tx_id, id_flag=True)
    t_id = dec_to_hex(socket.rx_id, id_flag=True)
    print(CYAN + f"[*] Writing DIDs for TX: {s_id}, RX: {t_id}" + RESET)

    for k, v in diddata.items():
        if stop_event is not None and stop_event.is_set():
            return False
        senddata = UDS() / UDS_WDBI(dataIdentifier = int(k,16)) / Raw(load=binascii.a2b_hex(v))
        resp = Sender_Receiver(socket,raw(senddata))





def process_input(args):
    """
    Process input for groupsend command.

    Args:
        args (str): Command line arguments

    Returns:
        tuple: (data1, loopData, data2, n) where:
            data1: Messages to send once before the loop
            loopData: Messages to repeat in the loop
            data2: Messages to send once after the loop
            n: Number of times to repeat the loop
    """
    data1 = ""
    loopData = ""
    data2 = ""
    n = 1  # Default loop count

    # Find loop data in parentheses
    loop_start = args.find('(')
    loop_end = args.find(')')

    if loop_start == -1 or loop_end == -1 or loop_start > loop_end:
        return None, None, None, None

    # Extract data before, inside, and after parentheses
    before_loop = args[:loop_start].strip()
    loop_content = args[loop_start+1:loop_end].strip()
    after_loop = args[loop_end+1:].strip()

    # Process data before loop
    data1 = before_loop

    # Process loop data
    loopData = loop_content

    # Process data after loop and extract loop count if present
    if '-n' in after_loop:
        parts = after_loop.split('-n')
        if len(parts) >= 2:
            count_part = parts[1].strip().split()
            if count_part:
                try:
                    n = int(count_part[0])
                    # Remove the -n and count from after_loop
                    after_loop = parts[0] + ' '.join(count_part[1:])
                except ValueError:
                    return None, None, None, None

    data2 = after_loop.strip()

    return data1, loopData, data2, n


def getSocket(content, socketPool,length):
    formatlen = str(len(str(length)))
    suffixstr = '%(index)'+formatlen+'d/%(max)'+formatlen+'d'


    sockets = []

    for socket in socketPool:
        flag = 1
        for each in content:
            if int(each[0],16) == socket.tx_id and int(each[1],16) == socket.rx_id:
                bar = Bar(each[0]+' '+each[1], max=length,suffix=suffixstr)
                bar.goto(length)
                bar.finish()
                flag = 0
        if flag:
            sockets.append(socket)

    return sockets




class MyApp(cmd2.Cmd):
    def __init__(self):
        cmd2.Cmd.__init__(self)
        del cmd2.Cmd.do_edit
        del cmd2.Cmd.do_macro
        del cmd2.Cmd.do_run_pyscript
        del cmd2.Cmd.do_run_script
        del cmd2.Cmd.do_set
        del cmd2.Cmd.do_shell
        del cmd2.Cmd.do_shortcuts
        del cmd2.Cmd.do_alias
        self.changeflag = 0
        self.interface = 'can0'
        self.fd = False
        self.bs = None
        self.ext = False
        self.sourcelist = []
        self.destinationlist = []
        self.socketPool = []
        self.key = 'DC09FA752072EFEF56CAC511FA53A38F'
        self.padd = True
        if not os.path.exists('data'):
            os.makedirs('data')

        init_can()
        self.prompt = CYAN + 'CAN➤ ' + RESET
        self.aliases.update({
            'q': 'quit',
            'h': 'help'
        })
    def do_connect(self, args):
        '''
        Create socket use source address and target address.
        Usage:
            connect
        '''
        if len(self.sourcelist) == 0 or len(self.destinationlist) == 0:
            print(RED + '[!] Please set source and target address first.' + RESET)
            return False
        if len(self.socketPool)>0:
            self.socketPool = pop_socket(self.socketPool, self.fd)
        try:
            if self.fd == False:
                init_can()
                for i in range(len(self.sourcelist)):
                    isoTpSocket = ISOTPSocket(CANSocket(bustype='socketcan', channel=self.interface,fd=self.fd,
                                                        can_filters=[{'can_id': self.sourcelist[i], 'can_mask': Bitmask(self.sourcelist[i],self.destinationlist[i])},
                                                                    {'can_id': self.destinationlist[i], 'can_mask': Bitmask(self.sourcelist[i],self.destinationlist[i])}]),
                                                                    tx_id=self.sourcelist[i], rx_id=self.destinationlist[i],padding=self.padd,stmin=5,basecls=ISOTP)
                    isoTpSocket.outs.filter_warning_emitted = True
                    self.socketPool.append(isoTpSocket)
            else:
                init_canfd()
                for i in range(len(self.sourcelist)):
                    isoTpSocket = ISOTPNativeSocket(self.interface,tx_id=self.sourcelist[i],rx_id=self.destinationlist[i], padding=self.padd,basecls=ISOTP,fd=self.fd)
                    self.socketPool.append(isoTpSocket)


            self.changeflag = 0
            print('[+] Socket Created Success')
        except Exception as e:
            print(e)
            print(RED+"[!] Please set "+self.interface+" up"+RESET)
            return False

    def do_udsid(self, args):
        '''
        Scan Diagnostic ID.
        You can set scan range from begin(default 0x0) to end(default 0x7ff).
        You can set sniffer timeout(default 100ms).
        The results will be automatically set to the source and target.

        Usage:
            udsid
            udsid [-b --begin] [-e --end] [-t --timeout]
        '''
        begin = 0x0
        end = 0x7ff
        timeout = 100
        args = args.split(' ')
        try:
            opts,args = getopt.getopt(args, "b:e:t:", ["begin=", "end=","timeout="])
            for opt, arg in opts:
                if opt in ("-b", "--begin"):
                    begin = int(arg, 16)
                elif opt in ("-e", "--end"):
                    end = int(arg, 16)
                elif opt in ("-t", "--timeout"):
                    timeout = int(arg)
            if args and type(args[0])!=cmd2.parsing.Statement:
                raise getopt.GetoptError("Invalid argument")
        except getopt.GetoptError as msg:
            self.do_help('udsid')
            print("ERROR:", msg)
            return False

        if (begin > end):
            print(RED + "[!] Argument 'begin' must < 'end'"+ RESET)
            return False
        print('[*] Scanning UDS ID from 0x%x to 0x%x...' % (begin, end))
        logger.info('[*] Scanning UDS ID from 0x%x to 0x%x...' % (begin, end))
        source, target = scanner_uds_id(self.interface,self.fd,self.ext,begin, end,timeout)
        if len(source) > 0:
            with open('./data/udsid.txt', 'w') as file:
                for item1, item2 in zip(source, target):
                    file.write(f"{item1} {item2}\n")
            print("Result: ")
            print('+----------------+----------------+')
            print("|      {:10}|    {:10}  |".format('Send', 'Received'))
            print('+----------------+----------------+')
            for i in range(len(source)):
                print("|    {:10}  |    {:10}  |".format(source[i], target[i]))
            print('+----------------+----------------+')

            self.sourcelist = [int(x,16) for x in source]
            self.destinationlist = [int(x,16) for x in target]

            print('[*] UDS ID saved in ./data/udsid.txt')
            print('[*] Done!')
        else:
            print(RED+ "\n[!] No UDS ID found." + RESET)

    # def _send_func(self, ssocket,args):
    #     if args == '':
    #         print(RED+'[!] Please input send Data'+RESET)
    #         return False
    #     args = args.split(' ')
    #     signal.signal(signal.SIGINT, signal_handler)
    #     signal.signal(signal.SIGTERM, signal_handler)

    #     if self.changeflag:
    #         print(RED+'[!] Configuration Data has been changed, Please reconnect'+RESET)
    #         return False

    #     random_seed = False
    #     number = None
    #     seedTimeout = 0
    #     pt2e_flag = False
    #     filename = None
    #     pt2e_index = -1

    #     # 修改这部分逻辑
    #     try:
    #         if '-f' in args:
    #             idx = args.index('-f')
    #             if idx > 0 and (args[idx-1].lower() == '2e'):
    #                 pt2e_flag = True
    #                 pt2e_index = idx-1  # 记录2e的位置
    #                 filename = args[idx+1]
    #                 # 只移除2e、-f和filename参数，保留其他命令
    #                 args = [x for i, x in enumerate(args) if i != idx-1 and i != idx and i != idx+1]
    #             else:
    #                 filename = args.pop(args.index('-f')+1)
    #                 args.remove('-f')

    #             if not os.path.exists(filename):
    #                 print(RED+'[!] File Not Exists'+RESET)
    #                 return False

    #         if '-n' in args:
    #             random_seed = True
    #             number = int(args.pop(args.index('-n')+1),10)
    #             args.remove('-n')
    #             if '-t' in args:
    #                 seedTimeout = int(args.pop(args.index('-t')+1))
    #                 args.remove('-t')

    #         # 先处理所有非2e命令
    #         for each in args:
    #             if stop_event is not None and stop_event.is_set():
    #                 break

    #             if '27' == each[:2] and int(each[2:4],16) % 2 == 1:
    #                 if random_seed:
    #                     tmp_seed = getseed(number, each, ssocket,seedTimeout)
                        
    #                     with open('./data/seed.json','w') as f:
    #                         json.dump([], f)

    #                     with open('./data/seed.json','r') as f:
    #                         content = json.load(f)
    #                     content.append([dec_to_hex(ssocket.tx_id,id_flag=True),dec_to_hex(ssocket.rx_id,id_flag=True),tmp_seed])
    #                     with open('./data/seed.json','w') as f:
    #                         json.dump(content, f)
    #                 else:
    #                     seed_list = []
    #                     resp = Sender_Receiver(ssocket, each)
    #                     printSendermenu(each,1,ssocket)

    #                     if resp and resp[UDS].service != 0x7f:
    #                         printSendermenu(resp, 0, ssocket)
    #                         seed = binascii.b2a_hex(resp[UDS_SAPR].securitySeed).decode('utf-8')
    #                         seed_list.append(seed)
    #             else:
    #                 resp = Sender_Receiver(ssocket,each)
    #                 printSendermenu(each,1,ssocket)
    #                 if resp:
    #                     printSendermenu(resp, 0, ssocket)
    #                     if resp[UDS].service == 0x50:
    #                         dealSessionData(ssocket,resp)

    #         # 然后处理2e命令（如果有）
    #         if pt2e_flag:
    #             print('')
    #             diddata = getData(ssocket, filename)
    #             if diddata:
    #                 kk = pt2e(ssocket, diddata)
    #                 if kk is False:
    #                     return False

    #         print()

    #         if random_seed:
    #             print('[+] All the seeds have been saved in seed.json')
    #             print('[*] path: ',os.getcwd()+os.sep+'data/seed.json')

    #     except Exception as e:
    #         print(RED + f'[!] Error: {str(e)}' + RESET)
    #         return False

    def do_send(self, args):
        if len(self.socketPool) == 0:
            print(RED+'[!] Please connect first'+RESET)
            return False
        elif args == '':
            print(RED+'[!] Please input send Data'+RESET)
            return False
        args = args.split(' ')
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        if self.changeflag:
            print(RED+'[!] Configuration Data has been changed, Please reconnect'+RESET)
            return False

        random_seed = False
        number = None
        seedTimeout = 0
        pt2e_flag = False
        filename = None
        pt2e_index = -1
        s2k = False

        # 修改这部分逻辑
        try:
            if '-f' in args:
                idx = args.index('-f')
                if idx > 0 and (args[idx-1].lower() == '2e'):
                    pt2e_flag = True
                    pt2e_index = idx-1  # 记录2e的位置
                    filename = args[idx+1]
                    # 只移除2e、-f和filename参数，保留其他命令
                    args = [x for i, x in enumerate(args) if i != idx-1 and i != idx and i != idx+1]
                else:
                    filename = args.pop(args.index('-f')+1)
                    args.remove('-f')

                if not os.path.exists(filename):
                    print(RED+'[!] File Not Exists'+RESET)
                    return False

            if '-n' in args:
                random_seed = True
                number = int(args.pop(args.index('-n')+1),10)
                args.remove('-n')
                if '-t' in args:
                    seedTimeout = int(args.pop(args.index('-t')+1))
                    args.remove('-t')

            if '--s2k' in args:
                s2k = True
                args = [x for x in args if x != '--s2k']

            for socket in self.socketPool:
                if stop_event is not None and stop_event.is_set():
                    break

                # 先处理所有非2e命令
                for each in args:
                    if stop_event is not None and stop_event.is_set():
                        break

                    if '27' == each[:2] and int(each[2:4],16) % 2 == 1:
                        if random_seed:
                            tmp_seed = getseed(number, each, socket,seedTimeout)
                            if socket == self.socketPool[0]:
                                with open('./data/seed.json','w') as f:
                                    json.dump([], f)
                            with open('./data/seed.json','r') as f:
                                content = json.load(f)
                            content.append([dec_to_hex(socket.tx_id,id_flag=True),dec_to_hex(socket.rx_id,id_flag=True),tmp_seed])
                            with open('./data/seed.json','w') as f:
                                json.dump(content, f)
                        else:
                            seed_list = []
                            resp = Sender_Receiver(socket, each)
                            printSendermenu(each,1,socket)

                            if resp and resp[UDS].service != 0x7f:
                                printSendermenu(resp, 0, socket)
                                seed = binascii.b2a_hex(resp[UDS_SAPR].securitySeed).decode('utf-8')
                                seed_list.append(seed)
                    
                    elif '27' == each[:2] and int(each[2:4],16) % 2 == 0 and s2k and len(seed_list[0]):
                        if self.key:
                            seed_key = jl_dssad(seed_list[0],self.key)
                            resp = Sender_Receiver(socket,each+seed_key)
                            printSendermenu(each,1,socket)
                            if resp:
                                printSendermenu(resp, 0, socket)
                                if resp[UDS].service == 0x67:
                                    print('bypass 27......')
                        else:
                            print(RED+'[!] Please set -k first！！！'+RESET)

                    else:
                        resp = Sender_Receiver(socket,each)
                        printSendermenu(each,1,socket)
                        if resp:
                            printSendermenu(resp, 0, socket)
                            if resp[UDS].service == 0x50:
                                dealSessionData(socket,resp)

                # 然后处理2e命令（如果有）
                if pt2e_flag:
                    print('')
                    diddata = getData(socket, filename)
                    if diddata:
                        kk = pt2e(socket, diddata)
                        if kk is False:
                            return False

                print()

            if random_seed:
                print('[+] All the seeds have been saved in seed.json')
                print('[*] path: ',os.getcwd()+os.sep+'data/seed.json')

        except Exception as e:
            print(RED + f'[!] Error: {str(e)}' + RESET)
            return False

    def do_scan(self, args):
        """
        Scan support Services、Sessions and Security Levels
        You can specify session mode when scanning services(default 1001)

        Usage:
        scan   #means scan Services、Sessions and Security Levels
        scan name

        example:
        scan session
        scan security
        scan
        scan service session
        scan --session 1003 1002
        """
        if len(self.socketPool) == 0:
            print(RED+'[!] Please connect first'+RESET)
            return False
        if self.changeflag:
            print(RED+'[!] Some Values has been changed, Please reconnect first'+RESET)
            return False

        num = 0
        sessionMode = None
        if '--session' in args:
            args, sessionMode = args.split('--session')
            args = args.rstrip()
            sessionMode = sessionMode.lstrip()
            for each in sessionMode.split(' '):
                if each[:2] != '10':
                    print(RED+'[!] Enter the correct session data')
                    return False

        if args == '':
            num |= 0b111
        else:
            if 'session' in args:
                num |= 0b010
            if 'security' in args:
                num |= 0b001
            if 'service' in args:
                num |= 0b100

        # res = scan_func(self.socketPool,num,sessionMode,self._send_func)
        res = scan_func(self.socketPool,num,sessionMode)
        if res is False:
            return False

    def do_read_memory_by_address(self, args):
        """
        Read memory by address with configurable memory size length and address length.

        Usage: read_memory_by_address [-s SIZE_LEN] [-a ADDR_LEN] [-v] [-n COUNT] [--session SESSION1 SESSION2 ...] [--random|--order]

        Options:
            -s, --size_len: Memory size length (1-4)
            -a, --addr_len: Memory address length (1-4)
            -v, --verbose: Show negative response codes
            -n, --count: Number of requests to send (default: 10)
            --session: Session mode (e.g., --session 1003 1002)
            --random: Send random addresses (default)
            --order: Send sequential addresses
        """
        try:
            parser = argparse.ArgumentParser(description='Read memory by address')
            parser.add_argument('-s', '--size_len', type=int, help='Memory size length (1-4)')
            parser.add_argument('-a', '--addr_len', type=int, help='Memory address length (1-4)')
            parser.add_argument('-v', '--verbose', action='store_true', help='Show negative response codes')
            parser.add_argument('-n', '--count', type=int, default=10, help='Number of requests to send')
            parser.add_argument('--session', nargs='+', type=str, help='Session mode')
            group = parser.add_mutually_exclusive_group()
            group.add_argument('--random', action='store_true', help='Send random addresses (default)', default=True)
            group.add_argument('--order', action='store_true', help='Send sequential addresses')

            args = parser.parse_args(args.split())

            # Validate input ranges if provided
            if args.size_len and not 1 <= args.size_len <= 4:
                print(RED + "[!] Memory size length must be between 1 and 4" + RESET)
                return False

            if args.addr_len and not 1 <= args.addr_len <= 4:
                print(RED + "[!] Memory address length must be between 1 and 4" + RESET)
                return False

            for socket in self.socketPool:
                # Set session if provided
                if args.session:
                    for session in args.session:
                        if session[:2] != '10':
                            print(RED + '[!] Enter the correct session data' + RESET)
                            return False
                        # Sender_Receiver(socket, session)
                        resp = Sender_Receiver(socket,session,verbos=True,dump=False)
                        printSendermenu(session, 1, socket)
                        if resp:
                            printSendermenu(resp, 0, socket)
                            if resp[UDS].service == 0x50:
                                dealSessionData(socket, resp)

                # Use provided values or random choice from list
                memorySizeLen_list = [1, 2, 3, 4]
                memoryAddressLen_list = [1, 2, 3, 4]

                i = args.size_len if args.size_len else random.choice(memorySizeLen_list)
                j = args.addr_len if args.addr_len else random.choice(memoryAddressLen_list)

                # Generate base address for ordered mode
                base_addr = 0

                for count in range(args.count):
                    if args.order:
                        # Sequential address generation
                        addr_bytes = base_addr.to_bytes(i, byteorder='big')
                        memoryAddress = addr_bytes.hex()
                        base_addr += 1
                    else:
                        # Random address generation
                        memoryAddress = genRandom(i)

                    memorySize = genRandom(j)
                    memorySizeLen = dec_to_hex(i, digits=1)
                    memoryAddressLen = dec_to_hex(j, digits=1)

                    sendData = '23' + memorySizeLen + memoryAddressLen + memoryAddress + memorySize
                    
                    resp = Sender_Receiver(socket, sendData, verbos=args.verbose)

        except argparse.ArgumentError as e:
            print(RED + f"[!] Error: {str(e)}" + RESET)
            self.do_help('read_memory_by_address')
            return False



    def do_dumpdids(self,args):
        """
        Read Data by DID
        Must set the reading range by using ',' or '-' as the interval
        You can set the display style (default progress bar) and session mode (default 1001)
        You can also choose whether the file saved with DID data should be recreated or appended.

        Usage: dumpdids [-r --range] [-s --session] [-v --verbose]
                        [-d --data] [-c --create] [-a --add]
        """
        didrange = None
        sessionMode = None

        verboseMode = False
        dataMode = False

        createMode = False
        addMode = False

        sockets = self.socketPool[:]

        if len(self.socketPool) == 0:
            print(RED+'[!] Please connect first'+RESET)
            return False

        if self.changeflag:
            print(RED+'[!] Some Values has been changed, Please reconnect first'+RESET)
            return False

        try:
            parser = argparse.ArgumentParser(description='Process command line arguments.')

            parser.add_argument('-r','--range',required=True, nargs='+', type=str, help='List of dids')
            parser.add_argument('-s','--session', nargs='+', type=str, help='List of session numbers')
            parser.add_argument('-v', '--verbose', action='store_true', help='Enable verbose mode')
            parser.add_argument('-d', '--data', action='store_true', help='Enable data mode')
            parser.add_argument('-c', '--create', action='store_true', help='Enable create mode')
            parser.add_argument('-a', '--add', action='store_true', help='Enable add mode')
            args = parser.parse_args(args.args.split(' '))

            didrange = args.range if args.range else None
            sessionMode = args.session if args.session else None
            verboseMode = args.verbose
            dataMode = args.data
            createMode = args.create
            addMode = args.add
        except:
            return False

        if verboseMode and dataMode:
            print(RED+'[!] verbose and data cannot be enabled at the same time'+RESET)
            return False

        if createMode ^ addMode == False:
            print(RED+'[!] create and add cannot be enabled at the same time'+RESET)
            return False

        didrange = ','.join(didrange)
        output_list = []
        for part in didrange.split(','):
            if '-' in part:
                start, end = part.split('-')
                start = int(start,16)
                end = int(end,16)
                output_list.extend(range(start, end+1))
            else:
                output_list.append(int(part,16))
        output_list = sorted(set(output_list))

        # print('range:',didrange)
        # print('session:',sessionMode)
        # print('verbose:',verboseMode)
        # print('data:',dataMode)
        # print('create:',createMode)
        # print('add:',addMode)

        content = []

        if addMode:
            try:
                with open('./data/dids.json') as f:
                    content = json.load(f)
                sockets = getSocket(content, self.socketPool,len(output_list))

            except FileNotFoundError as e:
                    print(RED + '[!] Not Found file ./data/dids.json'+RESET)
                    print('[*] please use -c or --create parameter')
                    return False

        elif createMode:
            with open('./data/dids.json','w') as f:
                json.dump(content, f)

        # Store original signal handlers
        original_sigint = signal.getsignal(signal.SIGINT)
        original_sigterm = signal.getsignal(signal.SIGTERM)

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        length = len(output_list)
        formatlen = str(len(str(length)))
        suffixstr = '%(index)'+formatlen+'d/%(max)'+formatlen+'d'

        for socket in sockets:
            try:
                if sessionMode:
                    for each in sessionMode:
                        if each[:2] != '10':
                            print(RED+'[!] Session Data Invalid'+RESET)
                            return False
                        # Sender_Receiver(socket, each)
                        resp = Sender_Receiver(socket,each,verbos=True,dump=False)
                        printSendermenu(each, 1, socket)
                        if resp:
                            printSendermenu(resp, 0, socket)
                            if resp[UDS].service == 0x50:
                                dealSessionData(socket, resp)

                s_id = dec_to_hex(socket.tx_id,id_flag=True)
                t_id = dec_to_hex(socket.rx_id,id_flag=True)

                # Add this section to print TX/RX IDs when verbose or data mode is enabled
                if verboseMode or dataMode:
                    print(CYAN + f"\n[*] Scanning DID for TX: {s_id}, RX: {t_id}" + RESET)

                tmp_dids = {}
                bar = Bar(s_id+' '+t_id, max=length,suffix=suffixstr)

                if stop_event is not None and stop_event.is_set():
                    break

                for each in output_list:
                    if stop_event is not None and stop_event.is_set():
                        break

                    data =raw(UDS() / UDS_RDBI(identifiers=[each]))
                    uds_data = None

                    if verboseMode is False and dataMode is False:
                        uds_data = Sender_Receiver(socket,data,dump=True)
                        bar.next()

                    elif verboseMode or dataMode:
                        uds_data = Sender_Receiver(socket,data,verbos=verboseMode)

                    if uds_data and uds_data[UDS].service == 0x7f and uds_data[UDS_NR].negativeResponseCode == 0x11:
                        break

                    elif uds_data and uds_data[UDS].service == 0x62:
                        if uds_data.haslayer('Raw'):
                            rcv_data =  binascii.b2a_hex(uds_data[Raw].load).decode('utf-8')
                            if dataMode or verboseMode:
                                print(dec_to_hex(each,id_flag=True,digits=4),'\t',rcv_data)
                            tmp_dids[dec_to_hex(uds_data[UDS].dataIdentifier,id_flag=True,digits=4)] = rcv_data

                        # rcv_data =  binascii.b2a_hex(uds_data[Raw].load).decode('utf-8')
                        # if dataMode or verboseMode:
                        #     print(dec_to_hex(each,id_flag=True,digits=4),'\t',rcv_data)

                        # tmp_dids[dec_to_hex(uds_data[UDS].dataIdentifier,id_flag=True,digits=4)] = rcv_data

                content.append([s_id,t_id,tmp_dids])
                with open('./data/dids.json','w') as f_new:
                    json.dump(content, f_new)
                bar.finish()

            except KeyboardInterrupt:
                print('\nInterrupted')
                bar.finish()
                break
            except Exception as e:
                print(f'\nError: {e}')
                bar.finish()
                break
            finally:
                # Restore original signal handlers
                signal.signal(signal.SIGINT, original_sigint)
                signal.signal(signal.SIGTERM, original_sigterm)
                # Clear stop event
                if stop_event is not None:
                    stop_event.clear()
                print("\n[+] All DIDs are Saved in the dids.json file")
                print('[*] path: ',os.getcwd()+os.sep+'data/dids.json')

    def do_groupsend(self, args):
        '''
        Send UDS messages in a repeating sequence with optional pre/post commands.

        Usage:
            groupsend [pre_msgs] (loop_msgs) [-n count] [post_msgs]

        Arguments:
            pre_msgs     Messages to send once before the loop
            loop_msgs    Messages to repeat (must be in parentheses)
            -n count     Number of times to repeat the loop (default: 1)
            post_msgs    Messages to send once after the loop

        Examples:
            groupsend 1003 (2701 2702) -n 5
                # Send 1003 once, then repeat 2701 2702 five times

            groupsend (1003 2701) -n 10
                # Repeat 1003 2701 ten times

            groupsend 1003 (2701 2702aaaaaaaa) -n 10 1001
                # Send 1003, repeat 2701 2702aaaaaaaa ten times, then send 1001

        '''
        seed = []
        data1, loopData, data2, n = process_input(args)
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        if len(self.socketPool) == 0:
            print(RED+'[!] Please connect first'+RESET)
            return False
        elif n is None:
            print(RED+'Input data error'+RESET)
            return False
        elif '(' in data2 or ')' in data2:
            print(RED+'Input data error'+RESET)
            return False

        
        for each in self.socketPool:
            seedlist = []
            # Execute pre-loop commands
            if data1:
                for _data1 in data1.split(' '):
                    resp = Sender_Receiver(each,_data1,verbos=True,dump=False)
                    printSendermenu(_data1, 1, each)
                    if resp:
                        printSendermenu(resp, 0, each)
                        if resp[UDS].service == 0x50:
                            dealSessionData(each, resp)

            # Execute loop commands n times
            for i in range(n):
                if stop_event is not None and stop_event.is_set():
                    break

                for eachdata in loopData.split(' '):
                    if eachdata[:2] == '27':
                        tmp_seed = getseed(1, eachdata,each,0)
                        if len(tmp_seed) != 0:
                            seedlist.append(tmp_seed[0])
                    else:
                        Sender_Receiver(each,eachdata,verbos=True,dump=False)
            
            if each == self.socketPool[0]:
                with open('./data/seed.json','w') as f:
                    json.dump([], f)
            with open('./data/seed.json','r') as f:
                content = json.load(f)
            content.append([dec_to_hex(each.tx_id,id_flag=True),dec_to_hex(each.rx_id,id_flag=True),seedlist])
            with open('./data/seed.json','w') as f:
                json.dump(content, f)


            # Execute post-loop commands
            if data2:
                for _data2 in data1.split(' '):
                    resp = Sender_Receiver(each,_data2,verbos=True,dump=False)
                    printSendermenu(_data2, 1, each)
                    if resp:
                        printSendermenu(resp, 0, each)
                        if resp[UDS].service == 0x50:
                            dealSessionData(each, resp)
           

    def do_bruteKey(self, args):
        '''
        Brute force the key
        You can choose Enumerator session (default 1003) and -v show NRC(default False)
        and security level(default 2701) and key length(Bytes default As long as seed)

        Usage:
        bruteKey [--session] [--level] [-v --verbose] [--length]

        Example:
        bruteKey --session 1003 1002 --level 1 --length 4 -v
        bruteKey --session 1003 --level 9
        '''
        # Store original signal handlers
        original_sigint = signal.getsignal(signal.SIGINT)
        original_sigterm = signal.getsignal(signal.SIGTERM)

        try:
            sessionMode = ['1003']
            level = 1
            verbose = False
            length = None

            if len(self.socketPool) == 0:
                print(RED+'[!] Please connect first'+RESET)
                return False

            if self.changeflag:
                print(RED+'[!] Configuration Data has been changed, Please reconnect'+RESET)
                return False

            if args.args != '':
                try:
                    parser = argparse.ArgumentParser(description='Process command line arguments.')
                    parser.add_argument('--level', type=lambda x: int(x, 16), help='Security Level')
                    parser.add_argument('--length', type=lambda x: int(x, 10), help='Key length')
                    parser.add_argument('--session', nargs='+', type=str, help='List of session numbers')
                    parser.add_argument('-v', '--verbose', action='store_true', help='Enable verbose mode')
                    args = parser.parse_args(args.args.split(' '))
                    level = args.level if args.level is not None else 0x1
                    length = args.length if args.length is not None else None
                    sessionMode = args.session if args.session else ['1003']
                    verbose = args.verbose
                except:
                    print(RED+'[!] Error: '+"Invalid argument"+RESET)
                    self.do_help('bruteKey')
                    return False



            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)

            for socket in self.socketPool:
                if sessionMode:
                    for each in sessionMode:
                        if each[:2] != '10':
                            print(RED+'[!] Session Data Invalid'+RESET)
                            return False
                        # self._send_func(ssocket=socket, args=each)

                        resp = Sender_Receiver(socket,each,verbos=True,dump=False)
                        printSendermenu(each, 1, socket)
                        if resp:
                            printSendermenu(resp, 0, socket)
                            if resp[UDS].service == 0x50:
                                dealSessionData(socket, resp)
                                
                seed = None
                bar = None

                resp = Sender_Receiver(socket, '27'+ dec_to_hex(level,digits=2))

                if length is None and resp and resp[UDS].service == 0x67:
                    seed = binascii.b2a_hex(resp[UDS_SAPR].securitySeed).decode('utf-8')
                    length = len(seed)//2

                if length is None:
                    print(RED+'[!] Error: '+"Invalid length"+RESET)
                    continue

                end = '1'+'0'*length*2
                data = UDS() / UDS_SA(securityAccessType=level + 1,securityKey=None)

                if verbose is False:
                    formatlen = str(len(end))
                    suffixstr = '%(index)'+formatlen+'d/%(max)'+formatlen+'d'
                    bar = Bar('Processing', max=int(end,16),suffix=suffixstr)

                try:
                    for i in range(0,int(end,16)):
                        if verbose is False:
                            bar.next()
                        if stop_event is not None and stop_event.is_set():
                            break

                        data[UDS_SA].securityKey = binascii.a2b_hex(dec_to_hex(i,digits=length*2))
                        resp = Sender_Receiver(socket, raw(data), verbos=verbose)

                        if resp and (resp[UDS].service == 0x67) and (resp[UDS].securityAccessType % 2 == 0): 
                            print('\n[+] Success ','Seed: ',seed,'Key: ',dec_to_hex(i,digits=length*2))
                            break

                finally:
                    if bar:
                        bar.finish()

        finally:
            # Restore original signal handlers
            signal.signal(signal.SIGINT, original_sigint)
            signal.signal(signal.SIGTERM, original_sigterm)
            # Clear stop_event
            if stop_event is not None:
                stop_event.clear()

    def do_set(self,args):
        '''
        You can manually set some values.
        Such as: source_address、target_address、key、timeout(default 0.2s),
                blocksize、file(set target_address、source_address from file),
                interface(default can0)、canfd(default can)、extended_can_id(default False),
                p2_star_time(default 5s)

        Usage: set [-s --source] [-t --target] [-k --key] [-T --timeout]
                   [-b --bs] [-f --file] [-i --interface] [--canfd] [--can]
                   [--ext] [-p --pstar]
        '''
        global globalTimeout
        global global_p2_timeout
        global global_p2_star_time

        args = args.split(' ')
        try:
            options = getopt.getopt(
                    args,'t:s:k:T:b:f:i:d:p:',
                    ['target=', 'source=','key=','timeout=','bs=','file=','interface=','debug=','canfd','ext','padding','pstart='])
            for opt, arg in options[0]:
                if opt in ('-s', '--source'):
                    self.changeflag = 1
                    self.sourcelist = [int(x,16) for x in arg.split(',')]
                elif opt in ('-t', '--target'):
                    self.changeflag = 1
                    self.destinationlist = [int(x,16) for x in arg.split(',')]
                elif opt in ('-k', '--key'):
                    self.key = arg
                elif opt in ('-b', '--bs'):
                    self.bs = int(arg)-2
                elif opt in ('-T','--timeout'):
                    global_p2_timeout = float(arg)
                elif opt in ('-p','--pstart'):
                    global_p2_star_time = float(arg)

                elif opt in ('-k','--key'):
                    self.key = arg

                elif opt in ('-f', '--file'):
                    try:
                        with open(arg,'r') as f:
                            data = f.read()
                            self.sourcelist = [int(x.split()[0],16) for x in data.strip().split('\n')]
                            self.destinationlist = [int(x.split()[1],16) for x in data.strip().split('\n')]
                            self.changeflag = 1
                    except FileNotFoundError as e:
                        print(RED + '[!] Not Found '+arg+RESET)
                elif opt in ('--canfd'):
                    self.changeflag = 1
                    self.fd = True
                elif opt in ('--padding'):
                    self.changeflag = 1
                    if self.padd == True:
                        self.padd = False
                    elif self.padd == False:
                        self.padd = True

                elif opt in ('--can'):
                    self.changeflag = 1
                    self.fd = False
                    self.ext = False
                elif opt in ('--ext'):
                    self.changeflag = 1
                    self.ext = True
                elif opt in ('-i', '--interface'):
                    self.changeflag = 1
                    self.interface = arg
                elif opt in ('-d','--debug'):
                    if arg == 'tangl123456':
                        print('[+] Debug Mode is On')
                        self.debug = True

        except getopt.GetoptError as msg:
            self.do_help('set')
            print("ERROR:", msg)

    def do_enumeratorIOCBI(self, args):
        '''
        Scan and test UDS Input Output Control By Identifier (0x2F) service.

        This command scans for supported IOCBI functions by testing different DIDs and control parameters.
        It supports all standard IOCBI control parameters and can operate in different diagnostic sessions.

        Control Parameters (IOCP) [default: 0x0-0x3]:
            0x00: Return Control To ECU
            0x01: Reset To Default
            0x02: Freeze Current State
            0x03: Short Term Adjustment (requires data)

        Usage:
            enumeratorIOCBI [options]

        Options:
            --iocp <values>     Specify IOCP values to test (default: [0,1,2,3])
            -b, --begin <hex>   Starting DID (default: 0x0000)
            -e, --end <hex>     Ending DID (default: 0xFFFF)
            -f, --file <path>   Load DIDs from JSON file
            --session <sess>    Diagnostic session to use (default: 1001)
            --data <values>     Control data for IOCP=0x03 (default: 0x00)
                            Format: single values or ranges (hex)
                            Example: 01 02-05 FF tests values 01,02,03,04,05,FF
            -v, --verbose      Show negative response codes

        Examples:
            enumeratorIOCBI
            enumeratorIOCBI -b 1234 -e 3456 --session 1003 1002
            enumeratorIOCBI --iocp 1 2 3 --data 01 02-05 FF
            enumeratorIOCBI -f dids.json --session 1003
            enumeratorIOCBI -v --iocp 1 3
        '''
        if len(self.socketPool) == 0:
            print(RED+'[!] Please connect first'+RESET)
            return False

        if self.changeflag:
            print(RED+'[!] Configuration Data has been changed, Please reconnect'+RESET)
            return False

        begin = 0x0
        end = 0xffff
        sessionMode = None
        iocp = [0x0,0x1,0x2,0x3]
        file = None
        cm_data = ['0']
        datalen = 2
        verbose = False

        if args.args != '':
            try:
                parser = argparse.ArgumentParser(description='Process command line arguments.')
                parser.add_argument('--iocp', nargs='+', type=lambda x: int(x, 16), help='List of types')
                parser.add_argument('-b', '--begin', type=lambda x: int(x, 16), help='Beginning DID in hex')
                parser.add_argument('-e', '--end', type=lambda x: int(x, 16), help='Ending DID in hex')
                parser.add_argument('-f', '--file', help='DID json file')
                parser.add_argument('--session', nargs='+', type=str, help='List of session numbers')
                parser.add_argument('--data', nargs='+', type=str, help='List of send Data')
                parser.add_argument('-v', '--verbose', action='store_true', help='Enable verbose mode')
                args = parser.parse_args(args.args.split(' '))

                iocp = args.iocp if args.iocp else [0,1,2,3]
                begin = args.begin if args.begin is not None else 0x0
                end = args.end if args.end is not None else 0xffff
                file = args.file if args.file else None
                sessionMode = args.session if args.session else None
                cm_data = args.data if args.data else ['0']
                verbose = args.verbose
            except:
                print(RED+'[!] Error: '+"Invalid argument"+RESET)
                self.do_help('enumeratorIOCBI')
                return False

        if begin > end:
            print(RED+'[!] Error: begin > end'+RESET)
            return False

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        current_prefix = None
        pfx = {'00': '0x00 Return Control To ECU:',
               '01': '0x01 Reset To Default:',
               '02': '0x02 Freeze Current State:',
               '03': '0x03 Short Term Adjustment:'}

        for socket in self.socketPool:
            dids = []
            if file:
                try:
                    diddata = getData(socket, file)
                    if diddata:
                        for k, v in diddata.items():
                            dids.append(int(k,16))
                except FileNotFoundError:
                    print(RED+'[!] Error: '+"Not found "+file+RESET)
                    return False
            else:
                dids = [x for x in range(begin, end+1)]

            output_list = []
            sendCmdata = []
            iocp.sort(reverse=True)
            if 0x03 in iocp:
                cm_data = ','.join(cm_data)
                for part in cm_data.split(','):
                    if '-' in part:
                        startt, endt = part.split('-')
                        if len(startt) > datalen or len(endt) > datalen:
                            datalen = len(startt) if len(startt) > len(endt) else len(endt)
                        startt = int(startt,16)
                        endt = int(endt,16)
                        output_list.extend(range(startt, endt+1))
                    else:
                        if len(part) > datalen:
                            datalen = len(part)
                        output_list.append(int(part,16))
                output_list = sorted(set(output_list))
                if datalen % 2 == 1:
                    datalen += 1
                for each in output_list:
                    sendCmdata.append(dec_to_hex(each, digits=datalen))

                output_list = [UDS() / UDS_IOCBI(dataIdentifier=x_did,controlOptionRecord=0x03,controlEnableMaskRecord=binascii.a2b_hex(x_record)) for x_record,x_did in itertools.product(sendCmdata,dids)]
                iocp.remove(0x03)

            sendData = [UDS() / UDS_IOCBI(dataIdentifier=x_did,controlOptionRecord=x_record) for x_record,x_did in itertools.product(iocp,dids)]
            output_list.extend(sendData)

            if sessionMode:
                for each in sessionMode:
                    if each[:2] != '10':
                        print(RED+'[!] Session Data Invalid'+RESET)
                        return False
                    # self._send_func(ssocket=socket, args=each)

                    resp = Sender_Receiver(socket,each,verbos=True,dump=False)
                    printSendermenu(each, 1, socket)
                    if resp:
                        printSendermenu(resp, 0, socket)
                        if resp[UDS].service == 0x50:
                            dealSessionData(socket, resp)

            for each in output_list:
                if stop_event is not None and stop_event.is_set():
                    break
                resp = Sender_Receiver(socket, raw(each), verbos=verbose,dump=True)

                if verbose is False:
                    tmpeach = binascii.b2a_hex(raw(each)).decode('utf-8')

                    prefix = tmpeach[6:]
                    value = tmpeach[:]

                    if prefix != current_prefix:
                        if current_prefix is not None:
                            print()

                        if pfx.get(prefix):
                            print(pfx.get(prefix))
                        else:
                            print('0x'+prefix)
                        current_prefix = prefix

                    if resp and (resp[UDS].service != 0x7f or (resp[UDS_NR].negativeResponseCode != 0x12 and resp[UDS_NR].negativeResponseCode != 0x31 and resp[UDS_NR].negativeResponseCode != 0x7f)):
                        if resp[UDS].service == 0x6f:
                            print('+-----------------+')
                            print("|   {0}  |".format('IOCP Success'))
                            print('+---------+-------------+')
                            print("|  {:5}  |  {:10} |".format('Data',binascii.b2a_hex(raw(each)).decode('utf-8')))
                            print('+---------+-------------+')
                        else:
                            print(value)

                if verbose and resp and resp[UDS].service == 0x6f:
                    print('+-----------------+')
                    print("|   {0}  |".format('IOCP Success'))
                    print('+---------+-------------+')
                    print("|  {:5}  |  {:10} |".format('Data',binascii.b2a_hex(raw(each)).decode('utf-8')))
                    print('+---------+-------------+')


    def do_show_values(self,args):
        print("\nSource:",[hex(x) for x in self.sourcelist])
        print("Target:",[hex(x) for x in self.destinationlist])
        print("Interface:",self.interface)
        print("CAN FD:",self.fd)
        print("Extended CAN:",self.ext)
        print("Timeout:",global_p2_timeout)
        print("p2_star_time:",global_p2_star_time)
        print("Key:",self.key)
        print("Block size:",self.bs)
        print("Padding:",self.padd)

    def do_clear(self,args):
        subprocess.run('clear', shell=True)

    def postcmd(self, stop, line):
        '''
        Usage: ctrl+z
        Exit this application
        '''
        stop_event.clear()
        if stop:
            return True
        return False


    def do_quit(self, arg):
        if len(self.socketPool):
            pop_socket(self.socketPool,self.fd)
        logger.info("="*84)
        print('Recorded in: ', os.getcwd()+os.sep+'can.log')
        return True


if __name__ == '__main__':
    # MyApp().cmdloop()
    # loader = LoadingSpinner("Loading Module...")
    # loader.start()

    # 模拟一些耗时的操作和 is_valid 函数的调用
    result = isValidTime()

    # loader.stop()
    if result:
        MyApp().cmdloop()
    else:
        errorMsg()
