#!/usr/bin/env python3
"""
测试脚本：验证CAN通信改进
用于测试新增的调试功能和错误处理机制
"""

import subprocess
import time
import sys
import os

def run_command(cmd, timeout=10):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout)
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Command timed out"

def test_can_interface():
    """测试CAN接口是否可用"""
    print("=" * 50)
    print("测试CAN接口状态")
    print("=" * 50)
    
    # 检查can0接口
    ret, out, err = run_command("ip link show can0")
    if ret == 0:
        print("✓ can0接口存在")
        if "UP" in out:
            print("✓ can0接口已启动")
        else:
            print("⚠ can0接口未启动，尝试启动...")
            run_command("sudo ip link set can0 up type can bitrate 500000")
    else:
        print("✗ can0接口不存在")
        return False
    
    # 检查candump是否可用
    ret, out, err = run_command("which candump")
    if ret == 0:
        print("✓ candump工具可用")
    else:
        print("⚠ candump工具不可用")
    
    return True

def test_tool_basic():
    """测试工具基本功能"""
    print("\n" + "=" * 50)
    print("测试工具基本功能")
    print("=" * 50)
    
    # 测试工具是否能正常启动
    print("测试工具启动...")
    
    # 创建测试脚本
    test_script = """
import sys
sys.path.append('.')
from tool import MyApp

app = MyApp()
print("Tool initialized successfully")

# 测试设置命令
app.onecmd("set -s 704 -t 784")
print("Set command executed")

# 测试连接
app.onecmd("connect")
print("Connect command executed")

# 测试新的调试命令
app.onecmd("debug timing")
print("Debug timing executed")

app.onecmd("timeout")
print("Timeout command executed")

print("All basic tests completed")
"""
    
    with open("test_basic.py", "w") as f:
        f.write(test_script)
    
    ret, out, err = run_command("python3 test_basic.py")
    if ret == 0:
        print("✓ 工具基本功能正常")
        print(out)
    else:
        print("✗ 工具基本功能异常")
        print("错误输出:", err)
    
    # 清理测试文件
    if os.path.exists("test_basic.py"):
        os.remove("test_basic.py")

def test_security_levels():
    """测试安全级别扫描功能"""
    print("\n" + "=" * 50)
    print("测试安全级别扫描功能")
    print("=" * 50)
    
    print("创建安全级别测试脚本...")
    
    test_script = """
import sys
sys.path.append('.')
from tool import MyApp

app = MyApp()

# 设置测试参数
app.onecmd("set -s 704 -t 784")
app.onecmd("connect")

# 测试新的安全扫描功能
print("Testing security scan with range...")
app.onecmd("security_scan -r 0x01 0x20")

print("Security scan test completed")
"""
    
    with open("test_security.py", "w") as f:
        f.write(test_script)
    
    print("运行安全级别测试...")
    ret, out, err = run_command("python3 test_security.py")
    
    if ret == 0:
        print("✓ 安全级别扫描功能正常")
    else:
        print("⚠ 安全级别扫描可能需要实际ECU连接")
    
    # 清理测试文件
    if os.path.exists("test_security.py"):
        os.remove("test_security.py")

def show_usage_examples():
    """显示使用示例"""
    print("\n" + "=" * 50)
    print("使用示例和建议")
    print("=" * 50)
    
    examples = [
        ("基本连接测试", [
            "set -s 704 -t 784",
            "connect",
            "debug test"
        ]),
        ("调整超时设置（针对慢速ECU）", [
            "timeout -preset slow",
            "send 1003"
        ]),
        ("安全级别详细扫描", [
            "security_scan -s 1003 -r 0x01 0x50"
        ]),
        ("监控CAN总线流量", [
            "monitor -f 704 784 -t 30"
        ]),
        ("调试特定问题", [
            "debug timing",
            "debug queue",
            "timeout -p2 0.3 -p2star 8"
        ])
    ]
    
    for title, commands in examples:
        print(f"\n{title}:")
        for cmd in commands:
            print(f"  CAN➤ {cmd}")

def main():
    """主测试函数"""
    print("CAN通信工具改进验证脚本")
    print("此脚本将测试新增的功能和改进")
    
    # 测试CAN接口
    if not test_can_interface():
        print("\n⚠ CAN接口测试失败，某些功能可能无法正常工作")
    
    # 测试工具基本功能
    test_tool_basic()
    
    # 测试安全级别功能
    test_security_levels()
    
    # 显示使用示例
    show_usage_examples()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)
    print("\n针对您遇到的问题，建议尝试以下解决方案：")
    print("\n1. 数据包丢失问题：")
    print("   - 使用 'timeout -preset slow' 增加超时时间")
    print("   - 使用 'monitor -f 704 784' 监控实际CAN流量")
    print("   - 使用 'debug test' 测试基本连通性")
    
    print("\n2. SUB_FUNCTION_NOT_SUPPORTED (2711) 问题：")
    print("   - 使用 'security_scan -s 1003' 扫描支持的安全级别")
    print("   - 尝试其他安全级别，如 27 01, 27 03, 27 05 等")
    print("   - 确认ECU是否需要特定的会话模式")
    
    print("\n3. 一般调试建议：")
    print("   - 使用 'debug timing' 检查当前超时设置")
    print("   - 使用 'timeout' 命令调整P2和P2*超时")
    print("   - 检查CAN总线负载和干扰")

if __name__ == "__main__":
    main()
