# UDS DID数据读取工具

这个工具用于读取和解析UDS (Unified Diagnostic Services) DID (Data Identifier) 数据。

## 文件说明

### 数据文件
- `data/dids.json` - 包含UDS ID和对应DID数据的JSON文件

### 脚本文件
1. **`simple_read_dids.py`** - 简单版本，直接输出所有数据
2. **`read_dids.py`** - 增强版本，支持多种功能

## 使用方法

### 简单版本 (simple_read_dids.py)

最简单的使用方式，直接输出所有UDS ID和DID数据：

```bash
python3 simple_read_dids.py
```

输出格式：
```
UDS ID 0x704 0x784 dids:
0x0100: 02
0x0101: ffffff
0x0102: 8b
...

==================================================

UDS ID 0x705 0x785 dids:
0x0101: 000003
0x0102: 7a
...
```

### 增强版本 (read_dids.py)

支持多种功能的完整版本：

#### 1. 显示帮助信息
```bash
python3 read_dids.py --help
```

#### 2. 显示摘要信息
```bash
python3 read_dids.py --summary
```

输出示例：
```
UDS DID数据摘要:
------------------------------------------------------------
UDS ID对              DID数量      示例DID                         
------------------------------------------------------------
0x704 0x784        345        0x0100, 0x0101, 0x0102...
0x705 0x785        112        0x0101, 0x0102, 0x0103...
...
总计: 25 个UDS ID对, 1495 个DID条目
```

#### 3. 过滤特定UDS ID
```bash
python3 read_dids.py --filter 0x704
```

#### 4. 输出到文件
```bash
python3 read_dids.py --output output.txt
```

#### 5. 组合使用
```bash
# 过滤特定UDS ID并输出到文件
python3 read_dids.py --filter 0x705 --output uds_705.txt

# 生成摘要并保存到文件
python3 read_dids.py --summary --output summary.txt
```

#### 6. 指定文件路径
```bash
python3 read_dids.py --file /path/to/your/dids.json
```

## 命令行参数说明

### read_dids.py 参数

| 参数 | 简写 | 说明 | 示例 |
|------|------|------|------|
| `--file` | `-f` | 指定dids.json文件路径 | `--file data/dids.json` |
| `--output` | `-o` | 输出到文件而不是控制台 | `--output result.txt` |
| `--filter` | | 过滤特定的UDS ID | `--filter 0x704` |
| `--summary` | `-s` | 只显示摘要信息 | `--summary` |
| `--help` | `-h` | 显示帮助信息 | `--help` |

## 数据格式说明

### 输入格式 (dids.json)
```json
[
    [
        "0x704",           // UDS ID 1
        "0x784",           // UDS ID 2  
        {
            "0x0100": "02",     // DID: DATA
            "0x0101": "ffffff",
            ...
        }
    ],
    ...
]
```

### 输出格式
```
UDS ID 0x704 0x784 dids:
0x0100: 02
0x0101: ffffff
0x0102: 8b
...
```

## 示例用法

### 查看所有数据
```bash
python3 simple_read_dids.py
```

### 查看数据摘要
```bash
python3 read_dids.py -s
```

### 查看特定UDS ID的数据
```bash
python3 read_dids.py --filter 0x704
```

### 导出特定数据到文件
```bash
python3 read_dids.py --filter 0x705 -o uds_705_data.txt
```

## 注意事项

1. 确保 `data/dids.json` 文件存在且格式正确
2. 输出文件会覆盖同名的现有文件
3. 过滤功能支持部分匹配（例如：`0x70` 会匹配所有包含 `0x70` 的UDS ID）
4. 所有脚本都支持UTF-8编码

## 错误处理

脚本会处理以下常见错误：
- 文件不存在
- JSON格式错误
- 权限问题
- 编码问题

如果遇到问题，请检查：
1. 文件路径是否正确
2. JSON格式是否有效
3. 文件权限是否足够
