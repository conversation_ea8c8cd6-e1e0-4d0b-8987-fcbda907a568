#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的UDS DID数据读取脚本
读取dids.json文件并按指定格式输出：UDS ID 0x704 0x784 dids: 换行DID和DATA
"""

import json
import os

def main():
    """主函数"""
    # 查找dids.json文件
    file_path = None
    possible_paths = ["dids.json", "data/dids.json"]
    
    for path in possible_paths:
        if os.path.exists(path):
            file_path = path
            break
    
    if not file_path:
        print("错误: 找不到dids.json文件")
        return
    
    try:
        # 读取JSON文件
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 输出数据
        for i, entry in enumerate(data):
            if len(entry) >= 3:
                uds_id1 = entry[0]
                uds_id2 = entry[1]
                dids_data = entry[2]
                
                print(f"UDS ID {uds_id1} {uds_id2} dids:")
                
                # 输出每个DID和对应的DATA
                for did, data_value in dids_data.items():
                    print(f"{did}: {data_value}")
                
                # 如果不是最后一个条目，添加空行分隔
                if i < len(data) - 1:
                    print()
                    print("=" * 50)
                    print()
        
        print(f"\n处理完成! 共输出了 {len(data)} 个UDS ID条目")
        
    except FileNotFoundError:
        print(f"错误: 文件不存在 - {file_path}")
    except json.JSONDecodeError as e:
        print(f"错误: JSON格式错误 - {e}")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    main()
