#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UDS DID数据读取脚本
读取dids.json文件并按指定格式输出UDS ID和对应的DID数据
"""

import json
import os

def read_dids_json(file_path):
    """
    读取dids.json文件
    
    Args:
        file_path (str): JSON文件路径
        
    Returns:
        list: 解析后的JSON数据
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except FileNotFoundError:
        print(f"错误: 找不到文件 {file_path}")
        return None
    except json.JSONDecodeError as e:
        print(f"错误: JSON解析失败 - {e}")
        return None
    except Exception as e:
        print(f"错误: 读取文件失败 - {e}")
        return None

def print_summary(data):
    """
    打印数据摘要信息

    Args:
        data (list): JSON数据列表
    """
    if not data:
        print("没有数据可显示")
        return

    print("UDS DID数据摘要:")
    print("-" * 60)
    print(f"{'UDS ID对':<20} {'DID数量':<10} {'示例DID':<30}")
    print("-" * 60)

    total_dids = 0
    for entry in data:
        if len(entry) >= 3:
            uds_id1 = entry[0]
            uds_id2 = entry[1]
            dids_data = entry[2]
            did_count = len(dids_data)
            total_dids += did_count

            # 获取前3个DID作为示例
            sample_dids = list(dids_data.keys())[:3]
            sample_str = ", ".join(sample_dids)
            if len(dids_data) > 3:
                sample_str += "..."

            print(f"{uds_id1} {uds_id2:<12} {did_count:<10} {sample_str}")

    print("-" * 60)
    print(f"总计: {len(data)} 个UDS ID对, {total_dids} 个DID条目")

def format_output(data):
    """
    按指定格式输出UDS ID和DID数据

    Args:
        data (list): JSON数据列表
    """
    if not data:
        print("没有数据可显示")
        return

    for i, entry in enumerate(data):
        if len(entry) >= 3:
            uds_id1 = entry[0]
            uds_id2 = entry[1]
            dids_data = entry[2]

            print(f"UDS ID {uds_id1} {uds_id2} dids:")
            print()

            # 输出DID和DATA
            for did, data_value in dids_data.items():
                print(f"{did}: {data_value}")

            # 如果不是最后一个条目，添加分隔线
            if i < len(data) - 1:
                print("\n" + "="*50 + "\n")

def main():
    """主函数"""
    import argparse

    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='读取UDS DID数据并格式化输出')
    parser.add_argument('--file', '-f', help='指定dids.json文件路径')
    parser.add_argument('--output', '-o', help='输出到文件而不是控制台')
    parser.add_argument('--filter', help='过滤特定的UDS ID (例如: 0x704)')
    parser.add_argument('--summary', '-s', action='store_true', help='只显示摘要信息')

    args = parser.parse_args()

    # 确定文件路径
    if args.file:
        file_path = args.file
        if not os.path.exists(file_path):
            print(f"错误: 指定的文件不存在: {file_path}")
            return
    else:
        # 检查当前目录和data目录下的dids.json文件
        possible_paths = [
            "dids.json",
            "data/dids.json",
            os.path.join(os.getcwd(), "dids.json"),
            os.path.join(os.getcwd(), "data", "dids.json")
        ]

        file_path = None
        for path in possible_paths:
            if os.path.exists(path):
                file_path = path
                break

        if not file_path:
            print("错误: 找不到dids.json文件")
            print("请确保文件存在于以下位置之一:")
            for path in possible_paths:
                print(f"  - {path}")
            return

    print(f"正在读取文件: {file_path}")
    print("="*50)

    # 读取JSON数据
    data = read_dids_json(file_path)

    if data is not None:
        # 应用过滤器
        if args.filter:
            filtered_data = []
            for entry in data:
                if len(entry) >= 2 and (args.filter in entry[0] or args.filter in entry[1]):
                    filtered_data.append(entry)
            data = filtered_data
            if not data:
                print(f"没有找到匹配的UDS ID: {args.filter}")
                return

        # 输出处理
        if args.output:
            # 重定向输出到文件
            import sys
            original_stdout = sys.stdout
            try:
                with open(args.output, 'w', encoding='utf-8') as f:
                    sys.stdout = f
                    if args.summary:
                        print_summary(data)
                    else:
                        format_output(data)
                sys.stdout = original_stdout
                print(f"输出已保存到文件: {args.output}")
            except Exception as e:
                sys.stdout = original_stdout
                print(f"保存文件失败: {e}")
        else:
            # 输出到控制台
            if args.summary:
                print_summary(data)
            else:
                format_output(data)

        print(f"\n处理完成! 共处理了 {len(data)} 个UDS ID条目")
    else:
        print("读取数据失败")

if __name__ == "__main__":
    main()
